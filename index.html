<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Woodworking Cut Optimizer</title>
<script src="https://cdn.jsdelivr.net/gh/jakesgordon/bin-packing@master/js/packer.js"></script>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica,
          Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f4f7f9;
        color: #333;
        display: flex;
        flex-direction: column;
        min-height: 100vh;
      }

      .header {
        background-color: #2c3e50;
        color: white;
        padding: 1rem 1.5rem;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .header h1 {
        margin: 0;
        font-size: 1.8rem;
      }

      .main-container {
        display: flex;
        flex-grow: 1;
        padding: 1rem;
        gap: 1rem;
      }

      .panel {
        background-color: white;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow-y: auto;
      }

      .input-panel {
        flex: 1.2;
        min-width: 300px;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
      }
      .visualization-panel {
        flex: 2;
        min-width: 400px;
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      .results-panel {
        flex: 1;
        min-width: 280px;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
      }

      h2 {
        color: #34495e;
        border-bottom: 2px solid #e0e0e0;
        padding-bottom: 0.5rem;
        margin-top: 0;
        font-size: 1.3rem;
      }

      .form-group {
        margin-bottom: 1rem;
      }

      .form-group label {
        display: block;
        margin-bottom: 0.3rem;
        font-weight: 600;
        color: #555;
        font-size: 0.9rem;
      }

      .form-group input[type="text"],
      .form-group input[type="number"],
      .form-group select {
        width: calc(100% - 1rem);
        padding: 0.6rem 0.5rem;
        border: 1px solid #ccc;
        border-radius: 4px;
        box-sizing: border-box;
        font-size: 0.9rem;
      }

      .form-group input:focus,
      .form-group select:focus {
        border-color: #3498db;
        outline: none;
        box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
      }

      .inline-inputs {
        display: flex;
        gap: 0.5rem;
        align-items: center;
      }

      .inline-inputs .form-group {
        flex: 1;
        margin-bottom: 0;
      }

      .inline-inputs input[type="number"] {
        width: calc(100% - 1rem); /* Adjust for padding */
      }
      .inline-inputs select {
        width: 100%;
      }

      button {
        background-color: #3498db;
        color: white;
        padding: 0.7rem 1.2rem;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.95rem;
        transition: background-color 0.2s ease;
        font-weight: 500;
      }

      button:hover {
        background-color: #2980b9;
      }

      button.secondary {
        background-color: #e74c3c;
      }
      button.secondary:hover {
        background-color: #c0392b;
      }

      button.action-btn {
        background-color: #2ecc71;
      }
      button.action-btn:hover {
        background-color: #27ae60;
      }

      ul {
        list-style-type: none;
        padding: 0;
      }

      li {
        background-color: #ecf0f1;
        padding: 0.6rem 0.8rem;
        margin-bottom: 0.5rem;
        border-radius: 4px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.9rem;
      }

      li button {
        padding: 0.2rem 0.5rem;
        font-size: 0.8rem;
        background-color: #95a5a6;
      }
      li button:hover {
        background-color: #7f8c8d;
      }

      .item-buttons {
        display: flex;
        gap: 0.5rem;
      }

      .item-buttons button {
        padding: 0.2rem 0.5rem;
        font-size: 0.8rem;
      }

      .edit-material-btn, .edit-piece-btn {
        background-color: #f39c12 !important;
      }
      .edit-material-btn:hover, .edit-piece-btn:hover {
        background-color: #e67e22 !important;
      }

      .danger-btn {
        background-color: #e74c3c !important;
      }
      .danger-btn:hover {
        background-color: #c0392b !important;
      }

      #visualizationContainer {
        border: 1px solid #bdc3c7;
        width: 100%;
        height: auto;
        aspect-ratio: 16 / 9; /* Default, can be dynamic */
        background-color: #fff;
        max-height: 500px; /* Limit container height */
        position: relative;
      }

      .canvas-container {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        position: relative;
      }

      .sheet-navigation {
        display: flex;
        gap: 0.5rem;
        align-items: center;
        margin-top: 0.5rem;
      }

      .piece-info-panel {
        transition: opacity 0.2s ease-in-out;
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(4px);
      }

      .piece-info-panel:hover {
        opacity: 0.9;
      }

      #cuttingListOutput {
        width: 100%;
        height: 200px;
        font-family: "Courier New", Courier, monospace;
        font-size: 0.85rem;
        border: 1px solid #ccc;
        border-radius: 4px;
        padding: 0.5rem;
        white-space: pre-wrap;
        overflow-y: auto;
        background-color: #fdfdfd;
      }

      .footer {
        text-align: center;
        padding: 0.8rem;
        background-color: #34495e;
        color: #ecf0f1;
        font-size: 0.8rem;
      }

      /* Responsive */
      @media (max-width: 1024px) {
        .main-container {
          flex-direction: column;
        }
        .input-panel,
        .visualization-panel,
        .results-panel {
          flex: none;
          width: auto; /* Full width on smaller screens */
        }
        #visualizationContainer {
          max-height: 40vh;
        }
      }
      @media (max-width: 768px) {
        .inline-inputs {
          flex-direction: column;
          gap: 0.8rem; /* Increased gap for column layout */
          align-items: stretch;
        }
        .inline-inputs .form-group {
          margin-bottom: 0.8rem;
        }
      }
    </style>
  </head>
  <body>
    <header class="header">
      <h1>Woodworking Cut Optimizer</h1>
    </header>

    <main class="main-container">
      <section class="panel input-panel">
        <div>
          <h2><span aria-hidden="true">📁</span> Project Management</h2>
          <div id="projectSelectContainer">
            <!-- Project select component will be rendered here -->
          </div>
        </div>

        <div>
          <h2><span aria-hidden="true">📦</span> Base Materials Inventory</h2>
          <div id="materialFormContainer">
            <!-- Material form component will be rendered here -->
          </div>
          <div id="inventoryListContainer">
            <!-- Inventory list component will be rendered here -->
          </div>
        </div>

        <div>
          <h2><span aria-hidden="true">📐</span> Project Pieces</h2>
          <div id="pieceFormContainer">
            <!-- Piece form component will be rendered here -->
          </div>
          <div id="pieceListContainer">
            <!-- Piece list component will be rendered here -->
          </div>
        </div>

        <div>
          <h2><span aria-hidden="true">⚙️</span> Cutting Settings</h2>
          <div class="inline-inputs">
            <div class="form-group">
              <label for="sawKerf">Saw Kerf (Blade Thickness)</label>
              <input type="number" id="sawKerf" value="3" min="0" step="0.1" />
            </div>
            <div class="form-group">
              <label for="kerfUnit">Kerf Unit</label>
              <select id="kerfUnit">
                <option value="mm">mm</option>
                <option value="cm">cm</option>
                <option value="in">inches</option>
              </select>
            </div>
          </div>
        </div>
      </section>

      <section class="panel visualization-panel">
        <h2><span aria-hidden="true">📊</span> Cutting Layout Visualization</h2>
        <button
          id="optimizeBtn"
          type="button"
          class="action-btn"
          style="width: 100%; margin-bottom: 1rem; padding: 0.8rem"
          aria-label="Optimize cutting plan"
        >
          OPTIMIZE CUTTING PLAN
        </button>
        <div class="canvas-container">
          <div
            id="visualizationContainer"
            role="img"
            aria-label="Visualization of cutting layout on a base material sheet."
          ></div>
          <div class="sheet-navigation">
            <button id="prevSheetBtn" type="button" aria-label="Previous sheet">
              &lt; Prev
            </button>
            <span id="sheetIndicator">Sheet 0 of 0</span>
            <button id="nextSheetBtn" type="button" aria-label="Next sheet">
              Next &gt;
            </button>
          </div>
        </div>
        <p
          id="optimizationMessage"
          aria-live="assertive"
          style="margin-top: 1rem; text-align: center"
        ></p>
      </section>

      <section class="panel results-panel">
        <div>
          <h2><span aria-hidden="true">📝</span> Cutting List</h2>
          <textarea
            id="cuttingListOutput"
            readonly
            aria-label="Detailed list of cuts"
          ></textarea>
        </div>
        <div>
          <h2><span aria-hidden="true">🛠️</span> Actions</h2>
          <button
            id="exportBtn"
            type="button"
            style="margin-right: 0.5rem"
            aria-label="Export cutting plan"
          >
            Export Plan
          </button>
          <button id="printBtn" type="button" aria-label="Print cutting plan">
            Print Plan
          </button>
        </div>
      </section>
    </main>

    <footer class="footer">
      <p>&copy; 2024 Woodworking Cut Optimizer. Client-side application.</p>
    </footer>

    <script type="module" src="index.tsx"></script>
  </body>
</html>
