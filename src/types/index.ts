/**
 * Core type definitions for the Woodworking Cut Optimizer
 */

// Basic unit types
export type Unit = "mm" | "cm" | "in";
export type GrainDirection = "none" | "length" | "width";

// Base material interface
export interface BaseMaterial {
  id: string;
  name: string;
  length: number;
  width: number;
  unit: Unit;
  quantity: number;
  thickness?: string;
}

// Project piece interface
export interface ProjectPiece {
  id: string;
  name: string;
  length: number;
  width: number;
  unit: Unit;
  quantity: number;
  grainDirection: GrainDirection;
  originalIndex?: number; // To map back to user's piece definition if needed
}

// Project interface
export interface Project {
  id: string;
  name: string;
  pieces: ProjectPiece[];
  sawKerf: number;
  kerfUnit: Unit;
  createdAt: Date;
  updatedAt: Date;
}

// Placed piece interface (after optimization)
export interface PlacedPiece extends ProjectPiece {
  x: number;
  y: number;
  packedWidth: number; // Width used for packing (includes kerf)
  packedHeight: number; // Height used for packing (includes kerf)
  sheetId: string;
  color: string;
}

// Optimized sheet layout interface
export interface OptimizedSheetLayout {
  sheetId: string;
  baseMaterial: BaseMaterial; // The specific sheet instance used
  pieces: PlacedPiece[];
  widthUsed: number; // Actual width of the base material in base units
  heightUsed: number; // Actual height of the base material in base units
}

// Type alias for pieces being prepared for packing
export type PieceForPacking = ProjectPiece & {
  uniqueId: string;
  packed: boolean;
  baseUnitWidth: number;
  baseUnitHeight: number;
  packedWidth: number;
  packedHeight: number;
};

// Interface for blocks processed by the Packer library
export interface PackerBlock {
  w: number; // width of the block
  h: number; // height of the block
  data: PieceForPacking; // The original piece data
  fit?: {
    // This property is added by the packer if the block is placed
    x: number;
    y: number;
  };
}

// Optimization request/response interfaces for API communication
export interface OptimizationRequest {
  materials: BaseMaterial[];
  pieces: ProjectPiece[];
  sawKerf: number;
  kerfUnit: Unit;
  projectId: string;
  userId?: string; // For authentication
}

export interface OptimizationResponse {
  success: boolean;
  layouts: OptimizedSheetLayout[];
  message?: string;
  error?: string;
  metadata?: {
    totalSheets: number;
    totalWaste: number;
    efficiency: number;
    processingTime: number;
  };
}

// Storage interface for abstraction
export interface StorageInterface {
  // Inventory operations
  getInventory(): Promise<BaseMaterial[]>;
  saveInventory(inventory: BaseMaterial[]): Promise<void>;
  
  // Project operations
  getProjects(): Promise<Project[]>;
  saveProjects(projects: Project[]): Promise<void>;
  getCurrentProjectId(): Promise<string | null>;
  setCurrentProjectId(projectId: string | null): Promise<void>;
  
  // Migration and cleanup
  migrateOldData(): Promise<void>;
  clearStorage(): Promise<void>;
}

// API client interface
export interface ApiClientInterface {
  // Authentication
  authenticate(credentials: AuthCredentials): Promise<AuthResponse>;
  refreshToken(): Promise<AuthResponse>;
  logout(): Promise<void>;
  
  // Optimization endpoints
  optimize(request: OptimizationRequest): Promise<OptimizationResponse>;
  
  // Project sync (future feature)
  syncProject(project: Project): Promise<Project>;
  getSharedProjects(): Promise<Project[]>;
}

// Authentication types
export interface AuthCredentials {
  username?: string;
  email?: string;
  password: string;
  apiKey?: string; // For API-only access
}

export interface AuthResponse {
  success: boolean;
  token?: string;
  refreshToken?: string;
  user?: UserInfo;
  error?: string;
}

export interface UserInfo {
  id: string;
  username: string;
  email: string;
  plan: 'free' | 'pro' | 'enterprise';
  permissions: string[];
}

// UI Component props interfaces
export interface MaterialFormProps {
  onAdd: (material: Omit<BaseMaterial, 'id'>) => void;
  onUpdate: (id: string, material: Omit<BaseMaterial, 'id'>) => void;
  editingMaterial?: BaseMaterial;
  onCancelEdit: () => void;
}

export interface PieceFormProps {
  onAdd: (piece: Omit<ProjectPiece, 'id'>) => void;
  onUpdate: (id: string, piece: Omit<ProjectPiece, 'id'>) => void;
  editingPiece?: ProjectPiece;
  onCancelEdit: () => void;
}

export interface ProjectSelectProps {
  projects: Project[];
  currentProjectId: string | null;
  onProjectChange: (projectId: string) => void;
  onCreateProject: (name: string) => void;
  onDeleteProject: (projectId: string) => void;
}

export interface VisualizationProps {
  layouts: OptimizedSheetLayout[];
  currentSheetIndex: number;
  onSheetChange: (index: number) => void;
  onPieceSelect?: (piece: PlacedPiece | null) => void;
}

// New UI Layout Component interfaces
export interface SidebarProps {
  currentView: 'projects' | 'inventory';
  projects: Project[];
  currentProjectId: string | null;
  onViewChange: (view: 'projects' | 'inventory') => void;
  onProjectSelect: (projectId: string) => void;
  onCreateProject: () => void;
}

export interface TabContainerProps {
  tabs: TabDefinition[];
  activeTabId: string;
  onTabChange: (tabId: string) => void;
}

export interface TabDefinition {
  id: string;
  label: string;
  content: HTMLElement | string;
  icon?: string;
}

export interface InventoryListProps {
  materials: BaseMaterial[];
  onEdit: (material: BaseMaterial) => void;
  onDelete: (id: string) => void;
  onDuplicate?: (id: string) => void;
}

export interface PieceListProps {
  pieces: ProjectPiece[];
  onEdit: (piece: ProjectPiece) => void;
  onDelete: (id: string) => void;
  onDuplicate?: (id: string) => void;
}

// Error types
export class OptimizationError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'OptimizationError';
  }
}

export class StorageError extends Error {
  constructor(
    message: string,
    public operation: string,
    public details?: any
  ) {
    super(message);
    this.name = 'StorageError';
  }
}

export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Configuration types
export interface AppConfig {
  baseUnit: Unit;
  defaultSawKerf: number;
  defaultKerfUnit: Unit;
  pieceColors: string[];
  apiEndpoint?: string;
  enableServerOptimization: boolean;
  enableAuthentication: boolean;
  maxLocalProjects: number;
  autoSaveInterval: number; // milliseconds
}

// Event types for the application
export interface AppEvents {
  'app:initialized': null;
  'project:created': Project;
  'project:updated': Project;
  'project:deleted': string; // project ID
  'project:loaded': Project;
  'material:added': BaseMaterial;
  'material:updated': BaseMaterial;
  'material:deleted': string; // material ID
  'piece:added': ProjectPiece;
  'piece:updated': ProjectPiece;
  'piece:deleted': string; // piece ID
  'optimization:started': OptimizationRequest;
  'optimization:completed': OptimizationResponse;
  'optimization:failed': Error;
  'storage:error': StorageError;
  'api:error': ApiError;
  'auth:login': UserInfo;
  'auth:logout': null;
  'auth:register': UserInfo;
  'auth:profile-updated': UserInfo;
}
