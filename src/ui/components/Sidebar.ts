/**
 * Sidebar Navigation Component
 * Provides navigation between Projects and Inventory sections
 * Shows project list for easy switching
 */

import { SidebarProps, Project } from '../../types';

export class Sidebar {
  private container: HTMLElement;
  private props: SidebarProps;

  constructor(container: HTMLElement, props: SidebarProps) {
    this.container = container;
    this.props = props;
    this.createSidebar();
    this.bindEvents();
    this.render();
  }

  /**
   * Update component props and re-render
   */
  updateProps(newProps: SidebarProps): void {
    this.props = newProps;
    this.render();
  }

  /**
   * Create the sidebar HTML structure
   */
  private createSidebar(): void {
    this.container.innerHTML = `
      <div class="sidebar">
        <div class="sidebar-header">
          <h1>Woodworking Cut Optimizer</h1>
        </div>
        
        <nav class="sidebar-nav">
          <div class="nav-section">
            <button class="nav-button" data-view="projects" aria-label="Switch to Projects view">
              <span class="nav-icon">📁</span>
              <span class="nav-label">Projects</span>
            </button>
            <button class="nav-button" data-view="inventory" aria-label="Switch to Inventory view">
              <span class="nav-icon">📦</span>
              <span class="nav-label">Inventory</span>
            </button>
          </div>
        </nav>

        <div class="projects-section">
          <div class="section-header">
            <h3>Projects</h3>
            <button class="create-project-btn" aria-label="Create new project">
              <span>+</span>
            </button>
          </div>
          <div class="projects-list">
            <!-- Projects will be rendered here -->
          </div>
        </div>

        <div class="sidebar-footer">
          <p>&copy; 2024 Cut Optimizer</p>
        </div>
      </div>
    `;
  }

  /**
   * Bind event listeners
   */
  private bindEvents(): void {
    // Navigation buttons
    const navButtons = this.container.querySelectorAll('.nav-button');
    navButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const view = (e.currentTarget as HTMLElement).dataset.view as 'projects' | 'inventory';
        this.props.onViewChange(view);
      });
    });

    // Create project button
    const createProjectBtn = this.container.querySelector('.create-project-btn');
    createProjectBtn?.addEventListener('click', () => {
      this.props.onCreateProject();
    });
  }

  /**
   * Render the sidebar content
   */
  private render(): void {
    this.updateNavigation();
    this.renderProjectsList();
  }

  /**
   * Update navigation button states
   */
  private updateNavigation(): void {
    const navButtons = this.container.querySelectorAll('.nav-button');
    navButtons.forEach(button => {
      const view = (button as HTMLElement).dataset.view;
      if (view === this.props.currentView) {
        button.classList.add('active');
      } else {
        button.classList.remove('active');
      }
    });
  }

  /**
   * Render the projects list
   */
  private renderProjectsList(): void {
    const projectsList = this.container.querySelector('.projects-list');
    if (!projectsList) return;

    if (this.props.projects.length === 0) {
      projectsList.innerHTML = `
        <div class="empty-state">
          <p>No projects yet</p>
          <small>Create your first project to get started</small>
        </div>
      `;
      return;
    }

    const projectsHTML = this.props.projects.map(project => `
      <div class="project-item ${project.id === this.props.currentProjectId ? 'active' : ''}" 
           data-project-id="${project.id}">
        <div class="project-info">
          <span class="project-name">${project.name}</span>
          <span class="project-meta">${project.pieces.length} pieces</span>
        </div>
      </div>
    `).join('');

    projectsList.innerHTML = projectsHTML;

    // Bind project selection events
    const projectItems = projectsList.querySelectorAll('.project-item');
    projectItems.forEach(item => {
      item.addEventListener('click', (e) => {
        const projectId = (e.currentTarget as HTMLElement).dataset.projectId;
        if (projectId) {
          this.props.onProjectSelect(projectId);
        }
      });
    });
  }

  /**
   * Get the sidebar element for styling
   */
  getSidebarElement(): HTMLElement {
    return this.container.querySelector('.sidebar') as HTMLElement;
  }

  /**
   * Destroy the component
   */
  destroy(): void {
    // Remove event listeners
    const navButtons = this.container.querySelectorAll('.nav-button');
    navButtons.forEach(button => {
      button.removeEventListener('click', () => {});
    });

    const createProjectBtn = this.container.querySelector('.create-project-btn');
    createProjectBtn?.removeEventListener('click', () => {});

    // Clear container
    this.container.innerHTML = '';
  }
}
