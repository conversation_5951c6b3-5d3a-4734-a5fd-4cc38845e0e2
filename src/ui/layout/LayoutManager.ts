/**
 * Layout Manager
 * Orchestrates the main application layout with sidebar and content areas
 * Manages view switching between Projects and Inventory sections
 */

import { Project, BaseMaterial } from '../../types';
import { Sidebar } from '../components/Sidebar';

export type AppView = 'projects' | 'inventory' | 'project-detail';

export interface LayoutManagerProps {
  onViewChange: (view: AppView, data?: any) => void;
  onProjectSelect: (projectId: string) => void;
  onCreateProject: () => void;
}

export class LayoutManager {
  private container: HTMLElement;
  private props: LayoutManagerProps;
  private sidebar: Sidebar | null = null;
  private currentView: AppView = 'projects';
  private currentProjectId: string | null = null;
  private projects: Project[] = [];

  // Layout elements
  private sidebarContainer: HTMLElement | null = null;
  private contentContainer: HTMLElement | null = null;

  constructor(container: HTMLElement, props: LayoutManagerProps) {
    this.container = container;
    this.props = props;
    this.createLayout();
    this.initializeSidebar();
  }

  /**
   * Create the main layout structure
   */
  private createLayout(): void {
    this.container.innerHTML = `
      <div class="app-layout">
        <aside class="app-sidebar">
          <!-- Sidebar component will be rendered here -->
        </aside>
        <main class="app-content">
          <!-- Content views will be rendered here -->
        </main>
      </div>
    `;

    this.sidebarContainer = this.container.querySelector('.app-sidebar');
    this.contentContainer = this.container.querySelector('.app-content');
  }

  /**
   * Initialize the sidebar component
   */
  private initializeSidebar(): void {
    if (!this.sidebarContainer) return;

    this.sidebar = new Sidebar(this.sidebarContainer, {
      currentView: this.currentView === 'project-detail' ? 'projects' : this.currentView,
      projects: this.projects,
      currentProjectId: this.currentProjectId,
      onViewChange: (view) => this.handleViewChange(view),
      onProjectSelect: (projectId) => this.handleProjectSelect(projectId),
      onCreateProject: () => this.handleCreateProject(),
    });
  }

  /**
   * Handle view changes from sidebar
   */
  private handleViewChange(view: 'projects' | 'inventory'): void {
    this.currentView = view;
    this.updateSidebar();
    this.props.onViewChange(view);
  }

  /**
   * Handle project selection from sidebar
   */
  private handleProjectSelect(projectId: string): void {
    this.currentProjectId = projectId;
    this.currentView = 'project-detail';
    this.updateSidebar();
    this.props.onProjectSelect(projectId);
  }

  /**
   * Handle create project from sidebar
   */
  private handleCreateProject(): void {
    this.props.onCreateProject();
  }

  /**
   * Update sidebar state
   */
  private updateSidebar(): void {
    if (!this.sidebar) return;

    this.sidebar.updateProps({
      currentView: this.currentView === 'project-detail' ? 'projects' : this.currentView,
      projects: this.projects,
      currentProjectId: this.currentProjectId,
      onViewChange: (view) => this.handleViewChange(view),
      onProjectSelect: (projectId) => this.handleProjectSelect(projectId),
      onCreateProject: () => this.handleCreateProject(),
    });
  }

  /**
   * Set the current view programmatically
   */
  setCurrentView(view: AppView, projectId?: string): void {
    this.currentView = view;
    if (projectId) {
      this.currentProjectId = projectId;
    }
    this.updateSidebar();
  }

  /**
   * Update projects list
   */
  updateProjects(projects: Project[]): void {
    this.projects = projects;
    this.updateSidebar();
  }

  /**
   * Set current project
   */
  setCurrentProject(projectId: string | null): void {
    this.currentProjectId = projectId;
    this.updateSidebar();
  }

  /**
   * Get the content container for rendering views
   */
  getContentContainer(): HTMLElement | null {
    return this.contentContainer;
  }

  /**
   * Get the sidebar container
   */
  getSidebarContainer(): HTMLElement | null {
    return this.sidebarContainer;
  }

  /**
   * Get current view
   */
  getCurrentView(): AppView {
    return this.currentView;
  }

  /**
   * Get current project ID
   */
  getCurrentProjectId(): string | null {
    return this.currentProjectId;
  }

  /**
   * Show loading state in content area
   */
  showLoading(message: string = 'Loading...'): void {
    if (!this.contentContainer) return;

    this.contentContainer.innerHTML = `
      <div class="loading-state">
        <div class="loading-spinner"></div>
        <p>${message}</p>
      </div>
    `;
  }

  /**
   * Show error state in content area
   */
  showError(message: string, onRetry?: () => void): void {
    if (!this.contentContainer) return;

    this.contentContainer.innerHTML = `
      <div class="error-state">
        <div class="error-icon">⚠️</div>
        <h3>Something went wrong</h3>
        <p>${message}</p>
        ${onRetry ? '<button class="retry-btn">Try Again</button>' : ''}
      </div>
    `;

    if (onRetry) {
      const retryBtn = this.contentContainer.querySelector('.retry-btn');
      retryBtn?.addEventListener('click', onRetry);
    }
  }

  /**
   * Clear content area
   */
  clearContent(): void {
    if (this.contentContainer) {
      this.contentContainer.innerHTML = '';
    }
  }

  /**
   * Destroy the layout manager
   */
  destroy(): void {
    if (this.sidebar) {
      this.sidebar.destroy();
      this.sidebar = null;
    }
    this.container.innerHTML = '';
  }
}
