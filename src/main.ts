/**
 * Main Application Entry Point
 * Orchestrates all components and manages application state
 */

import { 
  AppConfig, 
  AppEvents, 
  BaseMaterial, 
  ProjectPiece, 
  OptimizedSheetLayout,
  OptimizationRequest,
  StorageInterface
} from './types';
import { DEFAULT_CONFIG, EventEmitter, debounce } from './utils';
import { LocalStorage } from './storage/LocalStorage';
import { ApiStorage } from './storage/ApiStorage';
import { ApiClient } from './api/ApiClient';
import { AuthManager } from './auth/AuthManager';
import { MaterialManager } from './managers/MaterialManager';
import { ProjectManager } from './managers/ProjectManager';
import { OptimizationClient } from './optimization/OptimizationClient';
import { MaterialForm } from './ui/components/MaterialForm';
import { PieceForm } from './ui/components/PieceForm';
import { ProjectSelect } from './ui/components/ProjectSelect';
import { InventoryList } from './ui/components/InventoryList';
import { PieceList } from './ui/components/PieceList';
import { D3CanvasRenderer } from './ui/visualization/D3CanvasRenderer';

export class WoodworkingApp extends EventEmitter<AppEvents> {
  private config: AppConfig;
  private storage: StorageInterface;
  private apiClient: ApiClient | null = null;
  private authManager: AuthManager;
  private materialManager: MaterialManager;
  private projectManager: ProjectManager;
  private optimizationClient: OptimizationClient;

  // UI Components
  private materialForm: MaterialForm | null = null;
  private pieceForm: PieceForm | null = null;
  private projectSelect: ProjectSelect | null = null;
  private inventoryList: InventoryList | null = null;
  private pieceList: PieceList | null = null;
  private canvasRenderer: D3CanvasRenderer | null = null;

  // State
  private optimizedLayouts: OptimizedSheetLayout[] = [];
  private currentSheetIndex: number = 0;
  private isInitialized: boolean = false;

  // Auto-save
  private autoSaveInterval: NodeJS.Timeout | null = null;
  private debouncedSave = debounce(() => this.saveState(), 1000);

  constructor(config: Partial<AppConfig> = {}) {
    super();
    this.config = { ...DEFAULT_CONFIG, ...config };
    
    // Initialize storage
    this.storage = this.initializeStorage();
    
    // Initialize managers
    this.authManager = new AuthManager();
    this.materialManager = new MaterialManager(this.storage);
    this.projectManager = new ProjectManager(this.storage);
    this.optimizationClient = new OptimizationClient();

    this.bindManagerEvents();
  }

  /**
   * Initialize the application
   */
  async initialize(): Promise<void> {
    try {
      console.log('Initializing Woodworking Cut Optimizer...');

      // Initialize API client if configured
      if (this.config.apiEndpoint) {
        this.apiClient = new ApiClient(this.config.apiEndpoint);
        this.authManager.setApiClient(this.apiClient);
        this.optimizationClient.setApiClient(this.apiClient);
        this.optimizationClient.setServerOptimization(this.config.enableServerOptimization);
      }

      // Initialize managers
      await this.authManager.initialize();
      await this.materialManager.initialize();
      await this.projectManager.initialize();

      // Initialize UI components
      this.initializeUI();

      // Set up auto-save
      this.setupAutoSave();

      this.isInitialized = true;
      console.log('Application initialized successfully');

      // Emit initialization complete event
      this.emit('app:initialized', null);

    } catch (error) {
      console.error('Failed to initialize application:', error);
      throw error;
    }
  }

  /**
   * Initialize storage based on configuration
   */
  private initializeStorage(): StorageInterface {
    if (this.config.apiEndpoint && this.config.enableServerOptimization) {
      return new ApiStorage(this.config.apiEndpoint);
    } else {
      return new LocalStorage();
    }
  }

  /**
   * Initialize UI components
   */
  private initializeUI(): void {
    console.log('Initializing UI components...');

    // Initialize Material Form
    const materialFormContainer = document.querySelector('#materialFormContainer');
    console.log('Material form container found:', !!materialFormContainer);
    if (materialFormContainer) {
      this.materialForm = new MaterialForm(materialFormContainer as HTMLElement, {
        onAdd: (material) => this.handleAddMaterial(material),
        onUpdate: (id, material) => this.handleUpdateMaterial(id, material),
        onCancelEdit: () => this.handleCancelMaterialEdit(),
      });
      console.log('Material form initialized');
    }

    // Initialize Piece Form
    const pieceFormContainer = document.querySelector('#pieceFormContainer');
    console.log('Piece form container found:', !!pieceFormContainer);
    if (pieceFormContainer) {
      this.pieceForm = new PieceForm(pieceFormContainer as HTMLElement, {
        onAdd: (piece) => this.handleAddPiece(piece),
        onUpdate: (id, piece) => this.handleUpdatePiece(id, piece),
        onCancelEdit: () => this.handleCancelPieceEdit(),
      });
      console.log('Piece form initialized');
    }

    // Initialize Inventory List
    const inventoryListContainer = document.querySelector('#inventoryListContainer');
    if (inventoryListContainer) {
      this.inventoryList = new InventoryList(inventoryListContainer as HTMLElement, {
        materials: this.materialManager.getMaterials(),
        onEdit: (material) => this.handleEditMaterial(material),
        onDelete: (id) => this.handleDeleteMaterial(id),
        onDuplicate: (id) => this.handleDuplicateMaterial(id),
      });
    }

    // Initialize Piece List
    const pieceListContainer = document.querySelector('#pieceListContainer');
    if (pieceListContainer) {
      const currentProject = this.projectManager.getCurrentProject();
      this.pieceList = new PieceList(pieceListContainer as HTMLElement, {
        pieces: currentProject?.pieces || [],
        onEdit: (piece) => this.handleEditPiece(piece),
        onDelete: (id) => this.handleDeletePiece(id),
        onDuplicate: (id) => this.handleDuplicatePiece(id),
      });
    }

    // Initialize Project Select
    const projectSelectContainer = document.querySelector('#projectSelectContainer');
    console.log('Project select container found:', !!projectSelectContainer);
    if (projectSelectContainer) {
      this.projectSelect = new ProjectSelect(projectSelectContainer as HTMLElement, {
        projects: this.projectManager.getProjects(),
        currentProjectId: this.projectManager.getCurrentProjectId(),
        onProjectChange: (id) => this.handleProjectChange(id),
        onCreateProject: (name) => this.handleCreateProject(name),
        onDeleteProject: (id) => this.handleDeleteProject(id),
      });
      console.log('Project select initialized');
    }

    // Initialize D3 Canvas Renderer
    const container = document.querySelector('#visualizationContainer') as HTMLElement;
    const prevBtn = document.querySelector('#prevSheetBtn') as HTMLButtonElement;
    const nextBtn = document.querySelector('#nextSheetBtn') as HTMLButtonElement;
    const indicator = document.querySelector('#sheetIndicator') as HTMLSpanElement;

    if (container && prevBtn && nextBtn && indicator) {
      this.canvasRenderer = new D3CanvasRenderer(container, {
        layouts: this.optimizedLayouts,
        currentSheetIndex: this.currentSheetIndex,
        onSheetChange: (index: number) => this.handleSheetChange(index),
        onPieceSelect: (piece: any) => this.handlePieceSelect(piece),
      }, {
        prevButton: prevBtn,
        nextButton: nextBtn,
        sheetIndicator: indicator,
      });
    }

    // Bind other UI events
    this.bindUIEvents();

    console.log('UI initialization complete');
  }

  /**
   * Bind manager events
   */
  private bindManagerEvents(): void {
    // Material manager events
    this.materialManager.on('material:added', () => {
      this.debouncedSave();
      this.updateUI();
    });

    this.materialManager.on('material:updated', () => {
      this.debouncedSave();
      this.updateUI();
    });

    this.materialManager.on('material:deleted', () => {
      this.debouncedSave();
      this.updateUI();
    });

    // Project manager events
    this.projectManager.on('project:created', () => {
      this.debouncedSave();
      this.updateUI();
    });

    this.projectManager.on('project:updated', () => {
      this.debouncedSave();
      this.updateUI();
    });

    this.projectManager.on('project:deleted', () => {
      this.debouncedSave();
      this.updateUI();
    });

    this.projectManager.on('project:loaded', () => {
      this.clearOptimizationResults();
      this.updateUI();
    });

    // Optimization events
    this.optimizationClient.on('optimization:started', () => {
      this.updateOptimizationMessage('Optimizing...');
    });

    this.optimizationClient.on('optimization:completed', (response) => {
      this.optimizedLayouts = response.layouts;
      this.currentSheetIndex = 0;
      this.updateOptimizationMessage(
        `Optimization complete! ${response.layouts.length} sheet(s) used.`
      );
      this.updateCuttingList(response.layouts);
      this.updateUI();
    });

    this.optimizationClient.on('optimization:failed', (error) => {
      this.updateOptimizationMessage(`Optimization failed: ${error.message}`);
    });
  }

  /**
   * Bind UI events
   */
  private bindUIEvents(): void {
    console.log('Binding UI events...');

    // Test if basic buttons exist
    const createProjectBtn = document.querySelector('#createProjectBtn');
    const addMaterialBtn = document.querySelector('#addMaterialBtn');
    const addPieceBtn = document.querySelector('#addPieceBtn');

    console.log('Create Project button found:', !!createProjectBtn);
    console.log('Add Material button found:', !!addMaterialBtn);
    console.log('Add Piece button found:', !!addPieceBtn);

    // Add simple test handlers
    if (createProjectBtn) {
      createProjectBtn.addEventListener('click', () => {
        console.log('Create Project button clicked!');
      });
    }

    if (addMaterialBtn) {
      addMaterialBtn.addEventListener('click', () => {
        console.log('Add Material button clicked!');
      });
    }

    if (addPieceBtn) {
      addPieceBtn.addEventListener('click', () => {
        console.log('Add Piece button clicked!');
      });
    }

    // Optimize button
    const optimizeBtn = document.querySelector('#optimizeBtn');
    if (optimizeBtn) {
      optimizeBtn.addEventListener('click', () => this.handleOptimize());
    }

    // Export and print buttons
    const exportBtn = document.querySelector('#exportBtn');
    const printBtn = document.querySelector('#printBtn');
    
    if (exportBtn) {
      exportBtn.addEventListener('click', () => this.handleExport());
    }
    
    if (printBtn) {
      printBtn.addEventListener('click', () => this.handlePrint());
    }

    // Settings inputs
    const projectNameInput = document.querySelector('#projectName') as HTMLInputElement;
    const sawKerfInput = document.querySelector('#sawKerf') as HTMLInputElement;
    const kerfUnitSelect = document.querySelector('#kerfUnit') as HTMLSelectElement;

    if (projectNameInput) {
      projectNameInput.addEventListener('change', () => this.handleSettingsChange());
    }
    
    if (sawKerfInput) {
      sawKerfInput.addEventListener('change', () => this.handleSettingsChange());
    }
    
    if (kerfUnitSelect) {
      kerfUnitSelect.addEventListener('change', () => this.handleSettingsChange());
    }
  }

  /**
   * Handle material operations
   */
  private async handleAddMaterial(materialData: Omit<BaseMaterial, 'id'>): Promise<void> {
    try {
      await this.materialManager.addMaterial(materialData);
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to add material');
    }
  }

  private async handleUpdateMaterial(id: string, materialData: Omit<BaseMaterial, 'id'>): Promise<void> {
    try {
      await this.materialManager.updateMaterial(id, materialData);
      this.materialForm?.updateProps({
        onAdd: (material) => this.handleAddMaterial(material),
        onUpdate: (id, material) => this.handleUpdateMaterial(id, material),
        onCancelEdit: () => this.handleCancelMaterialEdit(),
      });
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to update material');
    }
  }

  private handleCancelMaterialEdit(): void {
    this.materialForm?.updateProps({
      onAdd: (material) => this.handleAddMaterial(material),
      onUpdate: (id, material) => this.handleUpdateMaterial(id, material),
      onCancelEdit: () => this.handleCancelMaterialEdit(),
    });
  }

  private handleEditMaterial(material: BaseMaterial): void {
    this.materialForm?.updateProps({
      onAdd: (material) => this.handleAddMaterial(material),
      onUpdate: (id, material) => this.handleUpdateMaterial(id, material),
      editingMaterial: material,
      onCancelEdit: () => this.handleCancelMaterialEdit(),
    });
  }

  private async handleDeleteMaterial(id: string): Promise<void> {
    try {
      await this.materialManager.removeMaterial(id);
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to delete material');
    }
  }

  private async handleDuplicateMaterial(id: string): Promise<void> {
    try {
      await this.materialManager.duplicateMaterial(id);
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to duplicate material');
    }
  }

  /**
   * Handle project operations
   */
  private async handleProjectChange(projectId: string): Promise<void> {
    try {
      await this.projectManager.switchToProject(projectId);
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to switch project');
    }
  }

  private async handleCreateProject(name: string): Promise<void> {
    try {
      const project = await this.projectManager.createProject(name);
      await this.projectManager.switchToProject(project.id);
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to create project');
    }
  }

  private async handleDeleteProject(projectId: string): Promise<void> {
    try {
      await this.projectManager.deleteProject(projectId);
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to delete project');
    }
  }

  /**
   * Handle piece operations
   */
  private async handleAddPiece(pieceData: Omit<ProjectPiece, 'id'>): Promise<void> {
    try {
      await this.projectManager.addPiece(pieceData);
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to add piece');
    }
  }

  private async handleUpdatePiece(id: string, pieceData: Omit<ProjectPiece, 'id'>): Promise<void> {
    try {
      await this.projectManager.updatePiece(id, pieceData);
      this.pieceForm?.updateProps({
        onAdd: (piece) => this.handleAddPiece(piece),
        onUpdate: (id, piece) => this.handleUpdatePiece(id, piece),
        onCancelEdit: () => this.handleCancelPieceEdit(),
      });
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to update piece');
    }
  }

  private handleCancelPieceEdit(): void {
    this.pieceForm?.updateProps({
      onAdd: (piece) => this.handleAddPiece(piece),
      onUpdate: (id, piece) => this.handleUpdatePiece(id, piece),
      onCancelEdit: () => this.handleCancelPieceEdit(),
    });
  }

  private handleEditPiece(piece: ProjectPiece): void {
    this.pieceForm?.updateProps({
      onAdd: (piece) => this.handleAddPiece(piece),
      onUpdate: (id, piece) => this.handleUpdatePiece(id, piece),
      editingPiece: piece,
      onCancelEdit: () => this.handleCancelPieceEdit(),
    });
  }

  private async handleDeletePiece(id: string): Promise<void> {
    try {
      await this.projectManager.removePiece(id);
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to delete piece');
    }
  }

  private async handleDuplicatePiece(id: string): Promise<void> {
    try {
      const currentProject = this.projectManager.getCurrentProject();
      if (!currentProject) return;

      const piece = currentProject.pieces.find(p => p.id === id);
      if (!piece) return;

      await this.projectManager.addPiece({
        name: `${piece.name} (Copy)`,
        length: piece.length,
        width: piece.width,
        unit: piece.unit,
        quantity: piece.quantity,
        grainDirection: piece.grainDirection,
      });
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to duplicate piece');
    }
  }

  /**
   * Handle optimization
   */
  private async handleOptimize(): Promise<void> {
    const currentProject = this.projectManager.getCurrentProject();
    if (!currentProject) {
      alert('No project selected');
      return;
    }

    const materials = this.materialManager.getMaterials();
    if (materials.length === 0) {
      alert('Please add materials to your inventory');
      return;
    }

    if (currentProject.pieces.length === 0) {
      alert('Please add pieces to your project');
      return;
    }

    const request: OptimizationRequest = {
      materials,
      pieces: currentProject.pieces,
      sawKerf: currentProject.sawKerf,
      kerfUnit: currentProject.kerfUnit,
      projectId: currentProject.id,
      userId: this.authManager.getCurrentUser()?.id,
    };

    await this.optimizationClient.optimize(request);
  }

  /**
   * Handle settings changes
   */
  private async handleSettingsChange(): Promise<void> {
    const currentProject = this.projectManager.getCurrentProject();
    if (!currentProject) return;

    const projectNameInput = document.querySelector('#projectName') as HTMLInputElement;
    const sawKerfInput = document.querySelector('#sawKerf') as HTMLInputElement;
    const kerfUnitSelect = document.querySelector('#kerfUnit') as HTMLSelectElement;

    const updates: any = {};

    if (projectNameInput?.value.trim()) {
      updates.name = projectNameInput.value.trim();
    }

    if (sawKerfInput?.value) {
      const sawKerf = parseFloat(sawKerfInput.value);
      if (!isNaN(sawKerf) && sawKerf >= 0) {
        updates.sawKerf = sawKerf;
      }
    }

    if (kerfUnitSelect?.value) {
      updates.kerfUnit = kerfUnitSelect.value;
    }

    try {
      await this.projectManager.updateProject(currentProject.id, updates);
    } catch (error) {
      console.error('Failed to update project settings:', error);
    }
  }

  /**
   * Handle piece selection
   */
  private handlePieceSelect(piece: any | null): void {
    if (piece) {
      console.log('Piece selected:', piece.name, piece);
      // You can add additional logic here, such as:
      // - Highlighting the piece in the piece list
      // - Showing additional piece details
      // - Updating other UI components
    } else {
      console.log('Piece deselected');
    }
  }

  /**
   * Handle sheet navigation
   */
  private handleSheetChange(index: number): void {
    this.currentSheetIndex = index;
    this.canvasRenderer?.updateProps({
      layouts: this.optimizedLayouts,
      currentSheetIndex: this.currentSheetIndex,
      onSheetChange: (index: number) => this.handleSheetChange(index),
      onPieceSelect: (piece: any) => this.handlePieceSelect(piece),
    });
  }

  /**
   * Handle export and print
   */
  private handleExport(): void {
    if (this.optimizedLayouts.length === 0) {
      alert('No cutting plan to export. Please optimize first.');
      return;
    }

    // Show export options
    const exportChoice = confirm(
      'Choose export format:\n\n' +
      'OK = Export complete plan (HTML with visualizations)\n' +
      'Cancel = Export cutting list only (TXT)'
    );

    const currentProject = this.projectManager.getCurrentProject();
    const projectName = currentProject?.name || 'cutting-plan';
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

    if (exportChoice) {
      // Export complete plan as HTML
      this.exportCompleteHTML(projectName, timestamp);
    } else {
      // Export cutting list only as TXT
      this.exportCuttingListTXT(projectName, timestamp);
    }
  }

  /**
   * Export cutting list as TXT file
   */
  private exportCuttingListTXT(projectName: string, timestamp: string): void {
    const cuttingListText = this.generateCuttingList(this.optimizedLayouts);
    const filename = `${projectName}-cutting-list-${timestamp}.txt`;

    const blob = new Blob([cuttingListText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const anchor = document.createElement('a');
    anchor.download = filename;
    anchor.href = url;
    anchor.click();
    URL.revokeObjectURL(url);
  }

  /**
   * Export complete plan as HTML file with visualizations
   */
  private exportCompleteHTML(projectName: string, timestamp: string): void {
    const cuttingListText = this.generateCuttingList(this.optimizedLayouts);

    // Get all sheet visualizations
    let visualizationsHTML = '';
    if (this.canvasRenderer) {
      const svgStrings = this.canvasRenderer.generateAllSheetsForPrint();

      svgStrings.forEach((svgString, index) => {
        visualizationsHTML += `
          <div class="sheet-section">
            <h2>Sheet ${index + 1} Layout</h2>
            <div class="svg-container">
              ${svgString}
            </div>
          </div>
        `;
      });
    }

    const htmlContent = this.generateCompleteHTMLContent(projectName, cuttingListText, visualizationsHTML);
    const filename = `${projectName}-complete-plan-${timestamp}.html`;

    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const anchor = document.createElement('a');
    anchor.download = filename;
    anchor.href = url;
    anchor.click();
    URL.revokeObjectURL(url);
  }

  private handlePrint(): void {
    if (this.optimizedLayouts.length === 0) {
      alert('No cutting plan to print. Please optimize first.');
      return;
    }

    const cuttingListText = this.generateCuttingList(this.optimizedLayouts);
    const currentProject = this.projectManager.getCurrentProject();
    const projectName = currentProject?.name || 'Cutting Plan';

    // Get all sheet visualizations
    let visualizationsHTML = '';
    if (this.canvasRenderer) {
      const svgStrings = this.canvasRenderer.generateAllSheetsForPrint();

      svgStrings.forEach((svgString, index) => {
        visualizationsHTML += `
          <div class="sheet-page">
            <h2>Sheet ${index + 1} Layout</h2>
            <div class="svg-container">
              ${svgString}
            </div>
          </div>
        `;
      });
    }

    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>${projectName} - Cutting Plan</title>
            <style>
              body {
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                margin: 0;
                padding: 20px;
                line-height: 1.4;
              }

              .cutting-list-page {
                page-break-after: always;
                margin-bottom: 40px;
              }

              .sheet-page {
                page-break-before: always;
                page-break-after: always;
                margin-bottom: 40px;
                text-align: center;
              }

              .sheet-page h2 {
                margin-bottom: 20px;
                color: #333;
                font-size: 24px;
              }

              .svg-container {
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 20px 0;
              }

              .svg-container svg {
                max-width: 100%;
                max-height: 80vh;
                border: 1px solid #ccc;
                background: white;
              }

              .cutting-list {
                font-family: 'Courier New', monospace;
                white-space: pre-wrap;
                font-size: 12px;
                line-height: 1.3;
                background: #f9f9f9;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 4px;
              }

              h1 {
                color: #333;
                border-bottom: 2px solid #333;
                padding-bottom: 10px;
                margin-bottom: 30px;
              }

              @media print {
                body { margin: 0; padding: 15px; }
                .cutting-list-page { page-break-after: always; }
                .sheet-page { page-break-before: always; page-break-after: always; }
                .svg-container svg { max-height: 70vh; }
              }
            </style>
          </head>
          <body>
            <div class="cutting-list-page">
              <h1>${projectName} - Cutting Plan</h1>
              <div class="cutting-list">${cuttingListText}</div>
            </div>
            ${visualizationsHTML}
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  }

  /**
   * Update UI components
   */
  private updateUI(): void {
    // Update project select
    this.projectSelect?.updateProps({
      projects: this.projectManager.getProjects(),
      currentProjectId: this.projectManager.getCurrentProjectId(),
      onProjectChange: (id) => this.handleProjectChange(id),
      onCreateProject: (name) => this.handleCreateProject(name),
      onDeleteProject: (id) => this.handleDeleteProject(id),
    });

    // Update inventory list
    this.inventoryList?.updateProps({
      materials: this.materialManager.getMaterials(),
      onEdit: (material) => this.handleEditMaterial(material),
      onDelete: (id) => this.handleDeleteMaterial(id),
      onDuplicate: (id) => this.handleDuplicateMaterial(id),
    });

    // Update piece list
    const currentProject = this.projectManager.getCurrentProject();
    this.pieceList?.updateProps({
      pieces: currentProject?.pieces || [],
      onEdit: (piece) => this.handleEditPiece(piece),
      onDelete: (id) => this.handleDeletePiece(id),
      onDuplicate: (id) => this.handleDuplicatePiece(id),
    });

    // Update canvas renderer
    this.canvasRenderer?.updateProps({
      layouts: this.optimizedLayouts,
      currentSheetIndex: this.currentSheetIndex,
      onSheetChange: (index: number) => this.handleSheetChange(index),
      onPieceSelect: (piece: any) => this.handlePieceSelect(piece),
    });

    // Update project settings in UI
    this.updateProjectSettingsUI();
  }

  /**
   * Update project settings in UI
   */
  private updateProjectSettingsUI(): void {
    const currentProject = this.projectManager.getCurrentProject();
    if (!currentProject) return;

    const projectNameInput = document.querySelector('#projectName') as HTMLInputElement;
    const sawKerfInput = document.querySelector('#sawKerf') as HTMLInputElement;
    const kerfUnitSelect = document.querySelector('#kerfUnit') as HTMLSelectElement;

    if (projectNameInput) projectNameInput.value = currentProject.name;
    if (sawKerfInput) sawKerfInput.value = currentProject.sawKerf.toString();
    if (kerfUnitSelect) kerfUnitSelect.value = currentProject.kerfUnit;
  }

  /**
   * Update optimization message
   */
  private updateOptimizationMessage(message: string): void {
    const messageEl = document.querySelector('#optimizationMessage');
    if (messageEl) {
      messageEl.textContent = message;
    }
  }

  /**
   * Generate cutting list text from optimized layouts
   */
  private generateCuttingList(layouts: OptimizedSheetLayout[]): string {
    if (layouts.length === 0) {
      return '';
    }

    let cuttingList = 'CUTTING LIST\n';
    cuttingList += '='.repeat(50) + '\n\n';

    layouts.forEach((layout, sheetIndex) => {
      cuttingList += `SHEET ${sheetIndex + 1}: ${layout.baseMaterial.name}\n`;
      cuttingList += `Material: ${layout.baseMaterial.length}${layout.baseMaterial.unit} × ${layout.baseMaterial.width}${layout.baseMaterial.unit}\n`;
      if (layout.baseMaterial.thickness) {
        cuttingList += `Thickness: ${layout.baseMaterial.thickness}\n`;
      }
      cuttingList += '-'.repeat(30) + '\n';

      // Group pieces by name for easier cutting
      const piecesByName = layout.pieces.reduce((groups, piece) => {
        const key = piece.name;
        if (!groups[key]) {
          groups[key] = [];
        }
        groups[key].push(piece);
        return groups;
      }, {} as Record<string, typeof layout.pieces>);

      Object.entries(piecesByName).forEach(([pieceName, pieces]) => {
        const firstPiece = pieces[0];
        cuttingList += `${pieces.length}x ${pieceName}\n`;
        cuttingList += `  Size: ${firstPiece.length}${firstPiece.unit} × ${firstPiece.width}${firstPiece.unit}\n`;
        if (firstPiece.grainDirection !== 'none') {
          cuttingList += `  Grain: ${this.formatGrainDirection(firstPiece.grainDirection)}\n`;
        }
        cuttingList += '\n';
      });

      cuttingList += '\n';
    });

    // Add summary
    const totalPieces = layouts.reduce((sum, layout) => sum + layout.pieces.length, 0);
    const totalSheets = layouts.length;

    cuttingList += 'SUMMARY\n';
    cuttingList += '='.repeat(20) + '\n';
    cuttingList += `Total pieces: ${totalPieces}\n`;
    cuttingList += `Total sheets: ${totalSheets}\n`;

    return cuttingList;
  }

  /**
   * Format grain direction for display
   */
  private formatGrainDirection(direction: string): string {
    switch (direction) {
      case 'length': return 'Along Length';
      case 'width': return 'Along Width';
      case 'none': return 'No Preference';
      default: return direction;
    }
  }

  /**
   * Update cutting list textarea
   */
  private updateCuttingList(layouts: OptimizedSheetLayout[]): void {
    const cuttingListEl = document.querySelector('#cuttingListOutput') as HTMLTextAreaElement;
    if (cuttingListEl) {
      cuttingListEl.value = this.generateCuttingList(layouts);
    }
  }

  /**
   * Generate complete HTML content for export
   */
  private generateCompleteHTMLContent(projectName: string, cuttingListText: string, visualizationsHTML: string): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${projectName} - Complete Cutting Plan</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.4;
            background: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
            font-size: 28px;
        }

        h2 {
            color: #2c3e50;
            margin-top: 40px;
            margin-bottom: 20px;
            font-size: 22px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }

        .cutting-list {
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            font-size: 12px;
            line-height: 1.3;
            background: #f8f9fa;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            margin-bottom: 40px;
            overflow-x: auto;
        }

        .sheet-section {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            background: #fafbfc;
        }

        .sheet-section h2 {
            margin-top: 0;
            text-align: center;
            color: #2c3e50;
        }

        .svg-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
            background: white;
            padding: 20px;
            border-radius: 4px;
        }

        .svg-container svg {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            text-align: center;
            color: #6c757d;
            font-size: 14px;
        }

        @media print {
            body { background: white; }
            .container { box-shadow: none; }
            .sheet-section { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>${projectName} - Complete Cutting Plan</h1>

        <h2>📝 Cutting List</h2>
        <div class="cutting-list">${cuttingListText}</div>

        <h2>📊 Sheet Layouts</h2>
        ${visualizationsHTML}

        <div class="footer">
            Generated on ${new Date().toLocaleString()} by Woodworking Cut Optimizer
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Clear optimization results
   */
  private clearOptimizationResults(): void {
    this.optimizedLayouts = [];
    this.currentSheetIndex = 0;
    this.updateOptimizationMessage('');
    this.updateCuttingList([]);
  }

  /**
   * Save application state
   */
  private async saveState(): Promise<void> {
    // State is automatically saved by managers
    // This method can be used for additional state saving if needed
  }

  /**
   * Set up auto-save
   */
  private setupAutoSave(): void {
    if (this.config.autoSaveInterval > 0) {
      this.autoSaveInterval = setInterval(() => {
        this.saveState();
      }, this.config.autoSaveInterval);
    }
  }

  /**
   * Destroy the application
   */
  destroy(): void {
    // Clear auto-save interval
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
    }

    // Destroy UI components
    this.materialForm?.destroy();
    this.pieceForm?.destroy();
    this.projectSelect?.destroy();
    this.inventoryList?.destroy();
    this.pieceList?.destroy();
    this.canvasRenderer?.destroy();

    // Remove all event listeners
    this.removeAllListeners();

    this.isInitialized = false;
  }
}


